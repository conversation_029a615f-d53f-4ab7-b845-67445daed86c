import { Route, Routes } from 'react-router-dom'
import { routesConfig } from './config'

interface RoutingProps {
  selectedText?: string;
  textOperation?: string;
}

export default ({ selectedText, textOperation }: RoutingProps) => {

  return (
    <Routes>
      {Object.values(routesConfig).map(({ path, Component }) => {
        // 将选中的文本和操作类型传递给页面组件
        return (
          <Route
            key={path}
            path={path}
            element={
              <Component
                selectedText={selectedText}
                textOperation={textOperation}
              />
            }
          />
        )
      })}
    </Routes>
  )
}
