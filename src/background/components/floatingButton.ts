import { EFloatButtonActionType, EContentsMessageType } from '@src/common/const'
import controlSidePanel, {
  type TMessageSender,
} from '../utils/controlSidePanel'
/**
 *
 * @param action 悬浮球的操作类型
 */
export const handleFloatingButtonAction = async (
  message: {
    type: EContentsMessageType.FloatingButton
    data: any
  },
  sender: TMessageSender,
  sendResponse: (response?: any) => void
) => {
  const { data } = message
  const action = data.action

  const { toggleSidePanel, openSidePanel, isSidePanelOpen } = controlSidePanel
  const delayTime = isSidePanelOpen ? 0 : 1000 // 如果侧边栏已经打开，则不需要延迟

  switch (data.action) {
    case EFloatButtonActionType.OpenPanel:
      // 处理侧边栏面板
      await toggleSidePanel(sender)
      break
    case EFloatButtonActionType.Translate:
      // 处理翻译操作
      await openSidePanel(sender)
      // 向侧边栏发送消息，请求翻译
      setTimeout(() => {
        chrome.runtime.sendMessage({
          type: EFloatButtonActionType.Translate,
        })
      }, delayTime)
      console.log('处理翻译操作')
      break
    case EFloatButtonActionType.Screenshot:
      // 处理截图操作
      await openSidePanel(sender)
      console.log('处理截图操作')
      break
    case EFloatButtonActionType.Summary:
      // 处理总结操作
      await openSidePanel(sender)
      // 向侧边栏发送消息，请求总结
      setTimeout(() => {
        chrome.runtime.sendMessage({
          type: EFloatButtonActionType.Summary,
        })
      }, delayTime)
      break
    default:
      console.warn('未知的悬浮球操作:', action)
  }
}
