/**
 * 文本操作工具类
 * 提供插入、替换等文本操作功能
 */

export interface TextOperationResult {
  success: boolean;
  message?: string;
  error?: string;
}

export class TextOperations {
  private static instance: TextOperations;
  private lastSelection: Selection | null = null;
  private lastRange: Range | null = null;

  private constructor() {}

  public static getInstance(): TextOperations {
    if (!TextOperations.instance) {
      TextOperations.instance = new TextOperations();
    }
    return TextOperations.instance;
  }

  /**
   * 缓存当前选择状态
   */
  public cacheCurrentSelection(): boolean {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      console.warn('TextOperations: No selection to cache');
      return false;
    }

    this.lastSelection = selection;
    this.lastRange = selection.getRangeAt(0).cloneRange();
    console.log('TextOperations: Selection cached');
    return true;
  }

  /**
   * 恢复缓存的选择状态
   */
  public restoreCachedSelection(): boolean {
    if (!this.lastRange) {
      console.warn('TextOperations: No cached selection to restore');
      return false;
    }

    try {
      const selection = window.getSelection();
      if (!selection) return false;

      selection.removeAllRanges();
      selection.addRange(this.lastRange);
      console.log('TextOperations: Selection restored');
      return true;
    } catch (error) {
      console.error('TextOperations: Failed to restore selection:', error);
      return false;
    }
  }

  /**
   * 替换选中的文本
   */
  public replaceSelectedText(newText: string): TextOperationResult {
    try {
      // 尝试恢复选择状态
      if (!this.restoreCachedSelection()) {
        return {
          success: false,
          error: '无法定位到原始选择位置'
        };
      }

      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) {
        return {
          success: false,
          error: '没有找到选中的文本'
        };
      }

      const range = selection.getRangeAt(0);
      
      // 检查是否在可编辑元素中
      const container = range.commonAncestorContainer;
      const editableElement = this.findEditableParent(container);

      if (editableElement) {
        // 在可编辑元素中直接替换
        range.deleteContents();
        range.insertNode(document.createTextNode(newText));
        
        // 触发输入事件，确保框架能检测到变化
        this.triggerInputEvents(editableElement);
      } else {
        // 在只读内容中，尝试通过DOM操作替换
        const textNode = document.createTextNode(newText);
        range.deleteContents();
        range.insertNode(textNode);
      }

      // 清除选择
      selection.removeAllRanges();
      
      return {
        success: true,
        message: '文本替换成功'
      };
    } catch (error) {
      console.error('TextOperations: Replace failed:', error);
      return {
        success: false,
        error: `替换失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 在选中文本下方插入新文本
   */
  public insertTextBelow(newText: string): TextOperationResult {
    try {
      // 尝试恢复选择状态
      if (!this.restoreCachedSelection()) {
        return {
          success: false,
          error: '无法定位到原始选择位置'
        };
      }

      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) {
        return {
          success: false,
          error: '没有找到选中的文本'
        };
      }

      const range = selection.getRangeAt(0);
      const container = range.commonAncestorContainer;
      const editableElement = this.findEditableParent(container);

      // 创建要插入的内容
      const insertContent = `\n\n${newText}`;

      if (editableElement) {
        // 在可编辑元素中插入
        const endRange = range.cloneRange();
        endRange.collapse(false); // 移动到选择的末尾
        endRange.insertNode(document.createTextNode(insertContent));
        
        // 触发输入事件
        this.triggerInputEvents(editableElement);
      } else {
        // 在只读内容中插入
        const endRange = range.cloneRange();
        endRange.collapse(false);
        
        // 尝试找到合适的插入位置（段落末尾）
        const insertionPoint = this.findInsertionPoint(endRange);
        if (insertionPoint) {
          const textNode = document.createTextNode(insertContent);
          insertionPoint.appendChild(textNode);
        } else {
          // 如果找不到合适的位置，就在当前位置插入
          endRange.insertNode(document.createTextNode(insertContent));
        }
      }

      // 清除选择
      selection.removeAllRanges();
      
      return {
        success: true,
        message: '文本插入成功'
      };
    } catch (error) {
      console.error('TextOperations: Insert failed:', error);
      return {
        success: false,
        error: `插入失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 查找可编辑的父元素
   */
  private findEditableParent(node: Node): Element | null {
    let current = node.nodeType === Node.TEXT_NODE ? node.parentElement : node as Element;
    
    while (current) {
      if (current.isContentEditable || 
          current.tagName === 'INPUT' || 
          current.tagName === 'TEXTAREA') {
        return current;
      }
      current = current.parentElement;
    }
    
    return null;
  }

  /**
   * 查找合适的插入位置
   */
  private findInsertionPoint(range: Range): Element | null {
    let current = range.endContainer;
    
    // 向上查找块级元素
    while (current && current.nodeType !== Node.ELEMENT_NODE) {
      current = current.parentNode;
    }
    
    while (current) {
      const element = current as Element;
      const tagName = element.tagName?.toLowerCase();
      
      // 查找段落、div等块级元素
      if (['p', 'div', 'article', 'section', 'main'].includes(tagName)) {
        return element;
      }
      
      current = current.parentNode;
    }
    
    return null;
  }

  /**
   * 触发输入相关事件，确保框架能检测到变化
   */
  private triggerInputEvents(element: Element): void {
    try {
      // 触发 input 事件
      const inputEvent = new Event('input', { bubbles: true, cancelable: true });
      element.dispatchEvent(inputEvent);
      
      // 触发 change 事件
      const changeEvent = new Event('change', { bubbles: true, cancelable: true });
      element.dispatchEvent(changeEvent);
      
      // 对于某些框架，可能还需要触发其他事件
      const keyupEvent = new KeyboardEvent('keyup', { bubbles: true, cancelable: true });
      element.dispatchEvent(keyupEvent);
    } catch (error) {
      console.warn('TextOperations: Failed to trigger events:', error);
    }
  }

  /**
   * 清理缓存的选择状态
   */
  public clearCache(): void {
    this.lastSelection = null;
    this.lastRange = null;
    console.log('TextOperations: Cache cleared');
  }
}

// 导出单例实例
export const textOperations = TextOperations.getInstance();
