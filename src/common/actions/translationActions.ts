/**
 * 翻译相关的Action函数
 */

import { updateIconWithTranslation } from '@src/contents/scripts/injectTranslate';
import type { BaseAction } from './index'
import { MessageType } from '../const';
import { getCurrentTab } from '@src/sidepanel/utils';

/**
 * 处理翻译API返回值的action函数
 * 这个函数封装了原本在批量翻译处理中的响应处理逻辑
 */
export const createInsertDomAction = (): BaseAction => {
  return {
    name: 'insertDom',
    description: '处理翻译API返回值，更新页面翻译图标',
    handler: async (response: any, group: any[]) => {
      console.log('handler', response);
      if (response.endFlag) {
        // 处理 response.list 数据结构
        if (response.list && Array.isArray(response.list)) {
          // 遍历 response.list 数组，提取每个对象的 content.text 字段
          const extractedTexts = response.list
            .filter((item: any) => item.content && item.content.text)
            .map((item: any) => item.content.text);

          // 将所有提取的文本拼接成一个完整的字符串
          const concatenatedText = extractedTexts.join('');

          // 如果成功提取到文本，则使用拼接后的字符串替换 response.result
          if (concatenatedText) {
            response.result = concatenatedText;
          }
        }

        console.log('response.result', response.result);

        if (response.result && response.result.includes('翻译结果：')) {
          // 提取翻译结果部分（去掉"翻译结果："前缀）
          const translationContent = response.result.replace(/^.*?翻译结果：/, '');
          // 使用'###'分割结果
          const results = translationContent.split('###');

          console.log('分割后的翻译结果:', results);

          const currentTab = await getCurrentTab()

          chrome.tabs.sendMessage(currentTab.id,
            {
              type: MessageType.INSERT_DOM,
              data: results,
            }
          )
        }
        else {
          // 处理错误情况
          console.error('翻译失败: 响应格式不正确或缺少必要的分隔符', response?.error || response?.result || '未知错误');
        }
      }


    }
  };
};

/**
 * 获取所有翻译相关的actions
 */
export const getTranslationActions = (): BaseAction[] => {
  return [
    createInsertDomAction()
  ];
};