import '@ht/chatui/dist/index.css'

import React, { useRef, Suspense, useEffect, forwardRef, useImperativeHandle } from 'react'
import { init, log } from '@ht/xlog'
import { isPrd } from '@src/common/utils'
import ChatUI from '@ht/chatui'
import { MessageType, EFloatButtonActionType } from '@src/common/const'
import { getAllActions } from '@src/common/actions'
import { composerConfig, config } from './config'
import * as styles from './index.module.less'

interface HiddenChatUIProps {
  selectedText?: string;
  textOperation?: string;
}

interface HiddenChatUIRef {
  chatContext: any;
  onSend: (type: string, content: string, options?: any, attachments?: any[]) => Promise<any>;
}

// 简化的Loading组件，避免复杂的UI渲染
const SimpleLoading = () => <div style={{ display: 'none' }}>Loading...</div>

// Content Script环境的消息API适配
const createContentScriptMessageAPI = () => {
  return {
    success: (content: string) => {
      console.log('HiddenChatUI Success:', content)
      // 可以在这里添加页面通知逻辑
    },
    error: (content: string) => {
      console.error('HiddenChatUI Error:', content)
      // 可以在这里添加页面错误通知逻辑
    },
    warning: (content: string) => {
      console.warn('HiddenChatUI Warning:', content)
    },
    info: (content: string) => {
      console.info('HiddenChatUI Info:', content)
    }
  }
}

// Content Script环境的翻译Hook适配
const useContentScriptTranslate = (chatUiRef: any) => {
  const messageApi = createContentScriptMessageAPI()

  const getUserId = () => {
    return "anonymous_user"
  }

  // 创建会话
  const createConversation = async (sendResponse?: (response: any) => void) => {
    try {
      const response = await fetch('http://*************:9607/ai/orchestration/session/createSession', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appId: 'web-assistant',
          userID: getUserId(),
        }),
      })

      const result = await response.json()
      console.log('会话创建成功:', result)

      if (sendResponse) {
        sendResponse(result)
      }

      return result
    } catch (error) {
      console.error('创建会话失败:', error)
      messageApi.error('创建会话失败')
      if (sendResponse) {
        sendResponse({ error: '创建会话失败' })
      }
      return null
    }
  }

  // 处理翻译请求
  const handleTranslate = async (query: string) => {
    console.log('HiddenChatUI handleTranslate:', query)
    if (chatUiRef?.current?.chatContext?.onSend) {
      const response = await chatUiRef.current.chatContext.onSend('text', `翻译此页面：${query}`, {
        agentId: 'translate'
      })
      console.log('HiddenChatUI translate response:', response)
      return response
    }
  }

  useEffect(() => {
    const messageHandler = (message: any, sender: any, sendResponse: any) => {
      if (message.type === MessageType.CREATE_CONVERSATION) {
        createConversation(sendResponse)
      } else if (message.type === MessageType.BATCH_TRANSLATE) {
        const { query } = message.data
        handleTranslate(query)
      } else if (message.type === MessageType.SINGLE_TRANSLATE) {
        const { query } = message.data
        handleTranslate(query)
      }
      return true
    }

    chrome.runtime.onMessage.addListener(messageHandler)

    return () => {
      chrome.runtime.onMessage.removeListener(messageHandler)
    }
  }, [chatUiRef])

  return null // 不返回contextHolder，因为我们不需要UI
}

const HiddenChatUI = forwardRef<HiddenChatUIRef, HiddenChatUIProps>(({ selectedText, textOperation }, ref) => {
  const chatUiRef = useRef<any>(null)
  const actions = getAllActions()

  // 初始化日志
  const initLog = () => {
    init({
      uuid: 'anonymous_user',
      from: 'HtscAiExtension_ContentScript',
      types: ['fetch', 'unhandledrejection', 'windowError'],
      myTrackConfig: {
        product_id: '366',
        product_name: 'Web开发平台',
        channel_env: isPrd ? 'prd_outer' : 'prd_outer_test',
      },
    })
  }

  useEffect(() => {
    initLog()
  }, [])

  // 使用Content Script适配的翻译Hook
  useContentScriptTranslate(chatUiRef)

  // 日志上报函数
  const onReportLog = (params: any) => {
    const { id, page_id, page_title, btn_id, btn_title } = params
    if (id) {
      log({
        id,
        page_id,
        page_title,
        btn_id,
        btn_title,
      })
    }
  }

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    get chatContext() {
      return chatUiRef.current?.chatContext
    },
    onSend: async (type: string, content: string, options?: any, attachments?: any[]) => {
      if (chatUiRef.current?.chatContext?.onSend) {
        return await chatUiRef.current.chatContext.onSend(type, content, options, attachments)
      }
      throw new Error('ChatUI not initialized')
    }
  }), [])

  return (
    <div className={styles.hiddenChatUIContainer}>
      <Suspense fallback={<SimpleLoading />}>
        <ChatUI
          navbar={{
            showLogo: false,
            showCloseButton: false,
            title: '',
          }}
          ref={chatUiRef}
          config={config}
          actions={actions}
          renderWelcome={() => null} // 不渲染欢迎页面
          onReportLog={onReportLog}
          inputOptions={{
            minRows: 2,
          }}
          composerConfig={composerConfig}
          renderFooterVersion={() => null} // 不渲染版本信息
          showStopAnswer={true}
          showToken={false}
          showHallucination={false}
        />
      </Suspense>
    </div>
  )
})

HiddenChatUI.displayName = 'HiddenChatUI'

export default HiddenChatUI
export type { HiddenChatUIRef }
