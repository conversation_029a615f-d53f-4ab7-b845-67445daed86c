/**
 * Mock AI 服务 - 用于开发和测试
 */

import { AIRequest, AIStreamCallback, AIService } from './aiService';

/**
 * Mock 响应数据
 */
const MOCK_RESPONSES: Record<string, (text: string) => string> = {
  'summary': (text: string) => `总结：${text.slice(0, 50)}${text.length > 50 ? '...' : ''}

核心要点已提取完成。`,

  'translate': (text: string) => `翻译：${text.slice(0, 30)}${text.length > 30 ? '...' : ''}

English: This is a sample translation of the provided text.`,

  'abbreviate': (text: string) => {
    const abbreviated = text.slice(0, Math.floor(text.length * 0.6));
    return `缩写：${abbreviated}

已压缩至 ${abbreviated.length} 字`;
  },

  'expand': (text: string) => `扩写：${text}

补充内容：进一步阐述了相关要点，丰富了表达内容。`,

  'polish': (text: string) => `润色：${text}

已优化语言表达，提升了文字流畅度。`,

  'correct': (text: string) => `修正：${text}

已检查语法和用词，确保表达准确。`
};

/**
 * Mock AI 服务类
 */
export class MockAIService extends AIService {
  /**
   * 模拟流式处理
   */
  async processText(
    request: AIRequest,
    onStream: AIStreamCallback,
    onError?: (error: Error) => void
  ): Promise<void> {
    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      const mockResponse = this.generateMockResponse(request.action, request.text);
      
      // 模拟流式响应
      let currentIndex = 0;
      const streamChunks = this.splitIntoChunks(mockResponse);
      
      for (const chunk of streamChunks) {
        // 模拟每个块的延迟
        await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
        
        onStream({
          content: chunk,
          isComplete: false
        });
      }

      // 完成处理
      onStream({
        content: '',
        isComplete: true
      });

    } catch (error) {
      console.error('Mock AI service error:', error);
      if (onError) {
        onError(error as Error);
      } else {
        onStream({
          content: '',
          isComplete: true,
          error: (error as Error).message
        });
      }
    }
  }

  /**
   * 生成 mock 响应
   */
  private generateMockResponse(action: string, text: string): string {
    const generator = MOCK_RESPONSES[action];
    if (!generator) {
      return `未知操作类型: ${action}`;
    }
    return generator(text);
  }

  /**
   * 将响应分割成流式块
   */
  private splitIntoChunks(text: string): string[] {
    const chunks: string[] = [];
    let currentIndex = 0;
    
    while (currentIndex < text.length) {
      const chunkSize = Math.floor(Math.random() * 15) + 5; // 5-20 字符的随机块
      const chunk = text.slice(currentIndex, currentIndex + chunkSize);
      chunks.push(chunk);
      currentIndex += chunkSize;
    }
    
    return chunks;
  }
}

/**
 * Mock AI 服务实例
 */
export const mockAIService = new MockAIService();
