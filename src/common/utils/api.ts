import axios from 'axios'

function buildApi<Req extends Record<string, any> | void, Res>(
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
  url: string,
  options?: {
    transform?: (res: any) => Res
    baseUrl?: string
    isNeedFullResponse?: boolean
    timeout?: number // 添加超时时间选项，单位为毫秒
  }
) {
  return async (params?: Req) => {
    let sendParams,
      reqUrl = url
    if (typeof params === 'object') {
      sendParams = { ...params }
    }
    
    const ret = await axios({
      method,
      url: `${options?.baseUrl ? options?.baseUrl : ''}${reqUrl}`,
      params: method === 'GET' ? sendParams : null,
      data: method !== 'GET' ? sendParams : null,
      timeout: options?.timeout || 10 * 1000, // 设置默认超时时间为10秒
      headers: {
        'x-web-system-id': 'ai-coding', // 'htsc-ai'，系统id
      },
    }).catch((err) => {
      throw err
    })
    if (ret.data.error) {
      throw ret.data.error
    }
    if (options?.isNeedFullResponse) {
      return ret
    }
    return options?.transform ? options.transform(ret.data) : (ret.data as Res)
  }
}

export { buildApi }
