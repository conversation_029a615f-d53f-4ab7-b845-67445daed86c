@import '../common/css/reset.less';

.settings-tab-page {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  background-color: #f5f5f5;

  /* 左侧导航栏 */
  .settings-sidebar {
    width: 400px;
    background-color: #fff;
    border-right: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    padding: 40px;

    font-family: PingFang SC;
    font-size: 18px;
    font-weight: 500;

    /* Logo区域 */
    .sidebar-logo {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 20px;

      img {
        width: 185px;
        height: 37px;
      }
    }

    /* 导航菜单 */
    .sidebar-nav {
      flex: 1;
      padding: 16px 0;

      .nav-item {
        display: flex;
        align-items: center;
        padding: 20px 20px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        color: #666;

        &:hover {
          background-color: #f0f0f0;
          color: #333;
        }

        &.active {
          background-color: #f5f5f5;
          border-radius: 8px;
        }

        .nav-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          width: 16px;
          height: 16px;
        }

        .nav-text {
          font-weight: 500;
        }
      }
    }
  }

  /* 右侧主内容区 */
  .settings-main {
    width: 900px;
    background-color: white;
    display: flex;
    flex-direction: column;

    .main-content {
      flex: 1;
      padding: 40px;
      max-width: 800px;
      margin: 0 auto;
      width: 100%;

      .settings-title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-bottom: 32px;
        text-align: left;
      }

      /* 悬浮窗预览区域 */
      .floating-preview {
        padding: 40px 40px 0 40px;
        background-color: #f2f5f9;
        img {
          width: 100%;
          height: 100%;
        }
      }

      /* 设置分组样式 */
      .settings-section {
        padding: 20px 40px;
        border: 1px solid #e8e8e8;
        border-radius: 8px;

        .setting-item {
          border-bottom: 1px solid #e8e8e8;
          padding: 16px 0;
          display: flex;
          align-items: center;
          justify-content: space-between;

          &:last-child {
            border-bottom: none;
            padding-bottom: 0;
          }

          &:first-child {
            padding-top: 0;
          }
        }
      }

      /* 设置项样式 */
      .setting-item {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .setting-label {
          color: #333;
          font-weight: 500;
          flex-shrink: 0;
          margin-right: 16px;
        }

        .ant-segmented {
          min-width: 200px;
        }
      }

      .selection-image {
        margin-top: 16px;
        height: 300px;
        padding: 40px 40px 0 40px;
        background-color: #f2f5f9;
        img {
          width: 100%;
          height: 100%;
        }
      }

      /* 禁用网站列表 */
      .disabled-websites-list {
        margin-top: 20px;

        .website-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          background-color: #fafafa;
          border-radius: 8px;
          margin-bottom: 12px;
          border: 1px solid #f0f0f0;

          .website-domain {
            font-size: 14px;
            color: #333;
            font-weight: 500;
          }

          .delete-button {
            color: #ff4d4f;
            padding: 4px 8px;

            &:hover {
              background-color: #fff2f0;
              color: #ff7875;
            }
          }
        }
      }

      /* 添加禁用网站弹窗 */
      .add-website-modal {
        .modal-description {
          color: #666;
          font-size: 14px;
          margin-bottom: 16px;
          line-height: 1.5;
        }

        .domain-input {
          height: 40px;
          border-radius: 6px;
          font-size: 14px;

          &:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }

      /* 用户资料卡片 */
      .user-profile-card {
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 40px;
        border: none;
        background-color: #fafafa;

        .user-info {
          display: flex;
          align-items: center;

          .user-avatar {
            margin-right: 20px;
            border: 2px solid #e8e8e8;
          }

          .user-details {
            flex: 1;

            .user-name {
              font-size: 18px;
              font-weight: 600;
              color: #333;
              margin-bottom: 6px;
            }

            .user-id {
              font-size: 14px;
              color: #666;
            }
          }
        }
      }

      .logout-section {
        width: 100%;
        display: flex;
        justify-content: center;
      }

      /* 划选工具页面 */
      .coming-soon {
        text-align: center;
        padding: 60px 20px;
        color: #666;
        font-size: 16px;
      }
    }
  }
}
