.htsc-chatui-navbar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;

  .settings-icon {
    position: absolute;
    top: 12px;
    right: 16px;

    .settings-icon-wrapper {
      width: 16px;
      height: 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      transition: color 0.2s ease;

      &:hover {
        color: #1890ff;
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .history-icon {
    width: 16px;
    height: 16px;
    cursor: pointer;
    position: absolute;
    top: 12px;
    right: 42px;
  }

  .ant-segmented {
    padding: 4px;
    border-radius: 18px;
    .ant-segmented-item {
      border-radius: 14px;
      overflow: hidden;
    }
    .ant-segmented-thumb {
      border-radius: 14px;
      overflow: hidden;
    }
  }
}
