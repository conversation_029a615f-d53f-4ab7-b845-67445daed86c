module.exports = {
  /* 设置prettier单行输出（不折行）的最大长度*/
  printWidth: 80,
  /* 设置工具每个水平缩进的空格数 */
  tabWidth: 2,
  /* 不使用tab制表位缩进 */
  useTabs: false,
  /* 不添加末尾分号 */
  semi: false,
  /* 单引号包含字符串 */
  singleQuote: true,
  trailingComma: 'es5',
  /* 括号空格 */
  bracketSpacing: true,
  /* 只有一个参数的箭头函数的参数是否带圆括号（默认avoid） */
  arrowParens: 'always',
  proseWrap: 'never',
  /* 优化html闭合标签不换行的问题 */
  htmlWhitespaceSensitivity: 'ignore',
  endOfLine: 'auto',
}
