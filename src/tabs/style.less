@import '../common/css/reset.less';

.upgrade-page {
  width: 1200px;
  margin: 24px auto;
  .title {
    font-weight: bold;
    font-size: 16px;
  }
  .version-list-wrap {
    margin-top: 24px;
    background-color: #ffffff;
    border-radius: 6px;
    border: 1px solid #d1d9e0;
    overflow: hidden;
    .header {
      background-color: #f6f8fa;
      padding: 16px;
      .version {
        font-weight: bold;
        color: #1f2328;
        font-size: 14px;
        display: flex;
        align-items: center;
        .version-name {
          margin-left: 6px;
        }
      }
    }
    .version-list-item {
      padding: 16px;
      border-top: 1px solid #d1d9e0b3;
      .version-num {
        font-size: 16px;
        font-weight: bold;
        color: #1f2328;
      }
      .change-wrap {
        color: #1f2328;
        margin-top: 16px;
        font-size: 14px;
        margin-left: 24px;
        .change-item {
          margin-top: 8px;
          list-style: inside;
          &:nth-of-type(1) {
            margin-top: 0;
          }
        }
      }
      .date-wrap {
        margin-top: 16px;
        display: flex;
        align-items: center;
        color: #59636e;
        font-size: 14px;

        .date-box,
        .zip-box {
          display: flex;
          align-items: center;
          color: #59636e;
        }
        .zip-box {
          margin-left: 16px;
          cursor: pointer;
        }
        .date,
        .zip {
          margin-left: 4px;
          font-size: 13px;
        }
      }
    }
  }
}
