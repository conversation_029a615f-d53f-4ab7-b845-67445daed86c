import { UpgradeIcon } from '@src/common/Icons'
import './style.less'

export default () => {
  const handleUpgrade = async () => {
    try {
      // 创建一个新的标签页，直接打开扩展的upgrade页面
      await chrome.tabs.create({
        url: `chrome-extension://${chrome.runtime.id}/tabs/upgrade.html`,
      })
    } catch (error) {
      console.error('Failed to open upgrade page:', error)
    }
  }

  return (
    <div className="footer-version">
      <div className="upgrade-container" onClick={handleUpgrade}>
        <UpgradeIcon />
        升级
      </div>
      以上内容均由AI生成，仅供参考和借鉴
    </div>
  )
}
