// 保存原始的fetch方法
const originalFetch = window.fetch

let globalConnectivity: {
  isTest: boolean
  isConnected?: boolean
  timestamp?: number
} = {
  isTest: false, // 是否正在测试网络联通性
}

export default (messageApi) => {
  // 测试网络联通性
  async function testConnectivity(url: string): Promise<boolean> {
    const now = Date.now()
    const duration = 1000 // 1000ms内，直接复用上次的网络联通性结果
    if (
      globalConnectivity.hasOwnProperty('isConnected') &&
      globalConnectivity.hasOwnProperty('timestamp') &&
      now - globalConnectivity.timestamp < duration
    ) {
      return globalConnectivity.isConnected
    }

    globalConnectivity.timestamp = now
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 1000) // 设置超时时间为1秒

      await originalFetch(url, {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache',
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      globalConnectivity.isConnected = true
      return true
    } catch (error) {
      messageApi.open({
        type: 'error',
        content: '网络不通，请在云桌面或连接HTSC重试！',
      })

      globalConnectivity.isConnected = false
      return false
    }
  }

  async function handleGlobalConnectivity(url) {
    globalConnectivity.isTest = true
    const isConnected = await testConnectivity(url)
    globalConnectivity.isTest = false
    return isConnected
  }

  // 代理XHR请求
  function proxyXHR() {
    const originalXHROpen = XMLHttpRequest.prototype.open
    const originalXHRSend = XMLHttpRequest.prototype.send

    XMLHttpRequest.prototype.open = function (method: string, url: string) {
      // 保存请求信息
      this._requestInfo = { method, url }
      return originalXHROpen.apply(this, arguments as any)
    }

    XMLHttpRequest.prototype.send = async function () {
      if (!this._requestInfo)
        return originalXHRSend.apply(this, arguments as any)

      const { url } = this._requestInfo

      try {
        const blankSubffixList = ['/collect/web/report', '/collect/web/config']
        const isBlankUrl = blankSubffixList.some((blankSubffix) =>
          url.endsWith(blankSubffix)
        )

        // 非黑名单url并且无正在测试请求时，测试网络联通性
        if (!globalConnectivity.isTest && !isBlankUrl) {
          const isConnected = await handleGlobalConnectivity(url)
          if (!isConnected) {
            // 网络不通
            return
          }
        }

        // 网络正常,继续发送请求
        return originalXHRSend.apply(this, arguments as any)
      } catch (eror) {}
    }
  }

  // 代理Fetch请求
  function proxyFetch() {
    window.fetch = async function (input: any) {
      try {
        const url = typeof input === 'string' ? input : input?.url

        // 无正在测试的请求时，测试网络联通性
        if (!globalConnectivity.isTest) {
          const isConnected = await handleGlobalConnectivity(url)
          if (!isConnected) {
            // 网络不通
            return
          }
        }

        // 使用原始的fetch方法发送请求
        return originalFetch.apply(this, arguments as any)
      } catch (error) {}
    }
  }

  proxyXHR()
  proxyFetch()
}
