const fs = require('fs')
const path = require('path')
const packageJson = require('../package.json')

async function renameZip() {
  try {
    const buildDir = path.join(__dirname, '../build')
    const sourcePath = path.join(buildDir, 'chrome-mv3-prod.zip')
    const targetPath = path.join(
      buildDir,
      `Web助手-v${packageJson.version}.zip`
    )

    // 检查源文件是否存在
    if (!fs.existsSync(sourcePath)) {
      console.error('源文件不存在:', sourcePath)
      return
    }

    // 重命名文件
    fs.rename(sourcePath, targetPath, (err) => {
      if (err) {
        console.error('重命名失败:', err)
      } else {
        console.log('文件重命名成功！')
        console.log('新文件名:', targetPath)
      }
    })
  } catch (error) {
    console.error('发生错误:', error)
  }
}

// 运行重命名函数
renameZip()
