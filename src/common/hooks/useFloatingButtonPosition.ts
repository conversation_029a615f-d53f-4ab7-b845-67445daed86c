import { useState } from 'react'

type TPosition = {
  right: number
  top: number
}
const useFloatingButtonPosition = () => {
  const POSITION_KEY = 'web-assistant-floatingButtonPosition'
  const defaultPosition: TPosition = { right: 8, top: window.innerHeight / 2 }

  const [position, setPosition] = useState<TPosition>(() => {
    try {
      const savedPosition = localStorage.getItem(POSITION_KEY)
      return savedPosition ? JSON.parse(savedPosition) : defaultPosition
    } catch (e) {
      // 解析失败，使用默认位置
      return defaultPosition
    }
  })

  const handlePositionChange = (position: TPosition) => {
    // 保存当前位置到本地存储
    try {
      localStorage.setItem(POSITION_KEY, JSON.stringify(position))
    } catch (e) {
      console.error('Failed to save button position:', e)
    }
  }

  return { position, setPosition, handlePositionChange }
}

export default useFloatingButtonPosition
