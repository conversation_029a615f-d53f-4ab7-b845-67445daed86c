// Content Script环境适配的ChatUI配置
// 基于 src/sidepanel/components/AiChat/aiChatConfig.ts 进行适配

// Content Script环境的getCurrentTab适配函数
const getCurrentTabForContentScript = async (): Promise<chrome.tabs.Tab | null> => {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({
      type: 'GET_CURRENT_TAB'
    }, (response) => {
      resolve(response || null)
    })
  })
}

// Content Script环境的页面翻译处理函数
const handleTranslateCurrentPageForContentScript = async () => {
  try {
    console.log('HiddenChatUI: 开始翻译页面...')
    // 在Content Script环境中，直接发送消息给翻译脚本
    chrome.runtime.sendMessage({
      type: 'START_TRANSLATE'
    })
  } catch (error) {
    console.error('HiddenChatUI: 翻译页面失败:', error)
  }
}

// Content Script环境的页面总结处理函数
const handleSummaryCurrentPageForContentScript = async () => {
  try {
    console.log('HiddenChatUI: 开始总结页面...')
    // 获取页面内容进行总结
    const pageContent = document.body.innerText || document.body.textContent || ''
    const plainText = pageContent.substring(0, 5000) // 限制长度
    
    return {
      content: plainText,
      url: window.location.href,
      title: document.title
    }
  } catch (error) {
    console.error('HiddenChatUI: 总结页面失败:', error)
    return null
  }
}

const skill = [
  {
    key: '',
    disabled: false,
    icon: '',
    label: '智能翻译',
    question: '翻译上面文字',
    agentId: '1',
    children: [
      {
        key: 'translate',
        disabled: false,
        icon: '',
        label: 'AI翻译',
        question: 'AI翻译',
        agentId: 'translate',
        onClick: () => console.log('HiddenChatUI: AI翻译'),
      },
      {
        key: 'translate-page',
        disabled: false,
        icon: '',
        label: '翻译此页面',
        question: '翻译此页面',
        agentId: 'translate-page',
        onClick: () => handleTranslateCurrentPageForContentScript(),
      }
    ],
    onClick: () => console.log('HiddenChatUI: 智能翻译'),
  },
  {
    key: 'summary',
    disabled: false,
    icon: '',
    label: '智能总结',
    question: '总结上面内容',
    agentId: 'summary',
    children: [
      {
        key: 'summary-text',
        disabled: false,
        icon: '',
        label: '总结文本',
        question: '总结文本',
        agentId: 'summary',
        onClick: () => console.log('HiddenChatUI: 总结文本'),
      },
      {
        key: 'summary-page',
        disabled: false,
        icon: '',
        label: '总结此页面',
        question: '总结此页面',
        agentId: 'summary',
        onClick: () => handleSummaryCurrentPageForContentScript(),
      }
    ],
    onClick: () => console.log('HiddenChatUI: 智能总结'),
  },
  {
    key: 'writing',
    disabled: false,
    icon: '',
    label: '写作助手',
    question: '帮我写作',
    agentId: 'writing',
    children: [
      {
        key: 'text-polisher',
        disabled: false,
        icon: '',
        label: '润色',
        question: '润色',
        agentId: 'text-polisher',
        onClick: () => console.log('HiddenChatUI: 润色'),
      },
      {
        key: 'grammar-corrector',
        disabled: false,
        icon: '',
        label: '修正拼写和语法',
        question: '修正拼写和语法',
        agentId: 'grammar-corrector',
        onClick: () => console.log('HiddenChatUI: 修正拼写和语法'),
      }
    ]
  }
]

const composerConfig = {
  quoteOperations: {
    citetext: [{
      disabled: false,
      icon: '',
      label: '翻译',
      question: '翻译',
      agentId: 'translate',
      customRender: '',
    },
    {
      disabled: false,
      icon: '',
      label: '总结',
      question: '总结',
      agentId: 'summary',
      customRender: '',
    },
    {
      disabled: false,
      icon: '',
      label: '润色',
      question: '润色',
      agentId: 'text-polisher',
      customRender: '',
    },
    {
      disabled: false,
      icon: '',
      label: '修正拼写和语法',
      question: '修正拼写和语法',
      agentId: 'grammar-corrector',
      customRender: '',
    }],
    citeweb: [{
      disabled: false,
      icon: '',
      label: '翻译',
      question: '翻译翻译',
      agentId: 'agent1',
      customRender: '',
    }],
    image: [{
      disabled: false,
      icon: '',
      label: '翻译',
      question: '翻译翻译',
      agentId: 'agent1',
      customRender: '',
    }],
    file: [{
      disabled: false,
      icon: '',
      label: '翻译',
      question: '翻译翻译',
      agentId: 'agent1',
      customRender: '',
    }],
  },
  skill,
}

// Content Script环境适配的配置
const config = {
  appId: 'web-assistant',
  userId: 'anonymous_user',
  requests: {
    baseUrl() {
      return ``
    },
    // Content Script环境的网络请求适配
    async requestTransfer(input: any) {
      // 在Content Script环境中，某些网络请求可能需要通过Background Script代理
      console.log('HiddenChatUI: Request transfer:', input)
      return input
    },
    headers: { 
      empid: '002332', 
      token: 'token', 
      'deviceId': 'deviceId' 
    },
  },
  // 问答接口配置
  send: {
    url: 'http://webassist.sit.sass.htsc/chat/workflow/chrome',
    createConversationUrl: 'http://*************:9607/ai/orchestration/session/createSession',
    isAibag: true,
    stream: true,
    messageInterval: 50,
  },
  // 查询历史详情接口
  history: {
    url: 'http://*************:9607/ai/orchestration/session/getHistoryMessages',
  },
  // 点赞点踩接口
  score: {
    url: 'http://*************:9607/ai/orchestration/session/feedback',
  },
  // 停止生成接口
  stop: {
    url: 'http://*************:9607/ai/orchestration/session/interruptSession',
  },
}

export { composerConfig, config, handleTranslateCurrentPageForContentScript, handleSummaryCurrentPageForContentScript }
