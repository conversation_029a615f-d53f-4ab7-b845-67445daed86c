import { message, But<PERSON> } from 'antd/es'
import { useNavigate } from 'react-router-dom'
import { chatLogo } from '@src/common/images'
import { EIP_CONFIG } from '@src/common/const'
import './style.less'

export default () => {
  const [messageApi, contextHolder] = message.useMessage()

  const handleLogin = () => {
    // 打开登录页面
    chrome.tabs.create({ url: EIP_CONFIG.LOGIN_PAGE })
  }

  return (
    <div className="login">
      <img src={chatLogo} className="chat-logo" />
      <Button 
        type="primary" 
        size="large" 
        className="login-btn"
        onClick={handleLogin}
      >
        登录
      </Button>
      {contextHolder}
    </div>
  )
}
