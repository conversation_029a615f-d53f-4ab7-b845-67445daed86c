/**
 * AI 处理 Hook
 */

import { useState, useCallback, useRef, useEffect } from 'react';

// 由于移动到common，需要动态导入AI服务
let getAIService: any = null;

// 动态导入AI配置
const loadAIService = async () => {
  if (!getAIService) {
    try {
      const aiConfig = await import('../../contents/config/aiConfig');
      getAIService = aiConfig.getAIService;
    } catch (error) {
      console.error('Failed to load AI service:', error);
      // 提供一个fallback
      getAIService = () => ({
        processText: () => Promise.reject(new Error('AI service not available')),
        getActionName: (action: string) => action
      });
    }
  }
  return getAIService();
};

// 导入AI服务相关类型
export interface AIRequest {
  action: string;
  text: string;
  options?: Record<string, any>;
}

export interface AIStreamResponse {
  content: string;
  isComplete: boolean;
  error?: string;
}

export interface AIProcessState {
  isProcessing: boolean;
  content: string;
  isComplete: boolean;
  error: string | null;
  processingTime: number;
}

export interface UseAIProcessReturn {
  state: AIProcessState;
  startProcess: (action: string, text: string, options?: Record<string, any>) => void;
  stopProcess: () => void;
  resetState: () => void;
}

/**
 * AI 处理 Hook
 */
export function useAIProcess(): UseAIProcessReturn {
  const [state, setState] = useState<AIProcessState>({
    isProcessing: false,
    content: '',
    isComplete: false,
    error: null,
    processingTime: 0,
  });

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const aiServiceRef = useRef<any>(null);

  // 初始化AI服务
  useEffect(() => {
    loadAIService().then(service => {
      aiServiceRef.current = service;
    });
  }, []);

  // 清理定时器
  const clearTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // 开始处理
  const startProcess = useCallback(async (
    action: string,
    text: string,
    options?: Record<string, any>
  ) => {
    if (!aiServiceRef.current) {
      setState(prev => ({
        ...prev,
        error: 'AI service not initialized',
        isComplete: true
      }));
      return;
    }

    // 重置状态
    setState({
      isProcessing: true,
      content: '',
      isComplete: false,
      error: null,
      processingTime: 0,
    });

    // 启动计时器
    timerRef.current = setInterval(() => {
      setState(prev => ({
        ...prev,
        processingTime: prev.processingTime + 1
      }));
    }, 1000);

    // 创建请求
    const request: AIRequest = {
      action,
      text,
      options,
    };

    // 处理流式响应
    const handleStream = (response: AIStreamResponse) => {
      setState(prev => {
        if (response.error) {
          return {
            ...prev,
            isProcessing: false,
            error: response.error,
            isComplete: true,
          };
        }

        if (response.isComplete) {
          return {
            ...prev,
            isProcessing: false,
            isComplete: true,
          };
        }

        return {
          ...prev,
          content: prev.content + response.content,
        };
      });
    };

    // 处理错误
    const handleError = (error: Error) => {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: error.message,
        isComplete: true,
      }));
    };

    try {
      await aiServiceRef.current.processText(request, handleStream, handleError);
    } catch (error) {
      handleError(error as Error);
    } finally {
      clearTimer();
    }
  }, [clearTimer]);

  // 停止处理
  const stopProcess = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    clearTimer();
    setState(prev => ({
      ...prev,
      isProcessing: false,
      isComplete: true,
    }));
  }, [clearTimer]);

  // 重置状态
  const resetState = useCallback(() => {
    stopProcess();
    setState({
      isProcessing: false,
      content: '',
      isComplete: false,
      error: null,
      processingTime: 0,
    });
  }, [stopProcess]);

  // 清理资源
  useEffect(() => {
    return () => {
      clearTimer();
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [clearTimer]);

  return {
    state,
    startProcess,
    stopProcess,
    resetState,
  };
}

export default useAIProcess;
