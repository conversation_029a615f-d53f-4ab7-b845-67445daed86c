import { Tooltip } from 'antd/es'
import { type NavbarProps } from '@ht/chatui'
import { log } from '@ht/xlog'
import { pcHistory } from '@src/common/images'
import { SettingIcon } from '@src/common/Icons'
import './style.less'

interface INavbarProps extends NavbarProps {
  hideHistory?: boolean
}

export default (props: INavbarProps) => {
  const { onHistoryButtonClick, hideHistory } = props

  const reportLog = (pageTitle: string) => {
    log({
      id: 'button_click',
      page_id: 'navbar',
      page_title: pageTitle,
    })
  }

  const handleHistoryButtonClick = () => {
    reportLog('查看历史会话')
    onHistoryButtonClick()
  }

  const handleSettingsClick = async () => {
    reportLog('打开配置页面')
    try {
      // 在新标签页中打开配置页面
      // 使用与upgrade页面相同的路径格式
      const settingsUrl = `chrome-extension://${chrome.runtime.id}/tabs/settings.html`

      console.log('Opening settings URL:', settingsUrl)
      await chrome.tabs.create({
        url: settingsUrl,
      })
    } catch (error) {
      console.error('Failed to open settings page:', error)
      // 如果打开新标签页失败，则显示错误信息
      console.error('Settings page navigation failed')
    }
  }

  return (
    <div className="htsc-chatui-navbar">
      {hideHistory ? null : (
        <Tooltip
          title="查看历史会话"
          placement="bottomRight"
          className="history-icon"
        >
          <img src={pcHistory} onClick={handleHistoryButtonClick} />
        </Tooltip>
      )}
      <Tooltip title="配置" placement="bottomRight" className="settings-icon">
        <div className="settings-icon-wrapper" onClick={handleSettingsClick}>
          <SettingIcon />
        </div>
      </Tooltip>
    </div>
  )
}
