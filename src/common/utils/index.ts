export * from './api'
export * from './env'
export * from './storage'
export { default as ipConnectivity } from './ipConnectivity'
export * from './userStorage'

export const isExtensionPage = (url: string) => {
  const urlPrefixes = [
    'chrome://',
    'chrome-extension://',
    'edge://',
    'about:',
    'https://chrome.google.com/webstore',
    'https://microsoftedge.microsoft.com/addons',
    'https://addons.mozilla.org/en-US/firefox',
    'https://chromewebstore.google.com/',
  ]
  return urlPrefixes.some((urlPrefix) => url.startsWith(urlPrefix))
}
