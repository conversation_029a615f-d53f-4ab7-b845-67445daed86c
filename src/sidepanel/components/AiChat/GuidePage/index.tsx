import React from 'react'
import { type GuidePageProps } from '@ht/chatui'
import { chatLogo } from '@src/common/images'
import { CustomIcon, SummaryIcon, TranslateIcon } from '@src/contents/Icon'
import Navbar from '../../Navbar'
import './style.less'

const GuidePage = React.forwardRef<HTMLDivElement, GuidePageProps>(
  (props, ref) => {
    const { navbar } = props

    return (
      <div ref={ref} className="guide-page">
        <Navbar {...navbar} />
        <div className="top-content">
          <h1 className='title'>你好!</h1>
          <h2 className='title'>我来了～</h2>
          <p>
            我可以帮你做、智能翻译、图片OCR识别、文档解析、语法修正等问题就好了～
          </p>
          <img className="chat-logo" src={chatLogo} />
        </div>
        <div className='chat-banners'>
          <div className='chat-banner'>
            <CustomIcon />
            <div className='chat-banner-title'>
              语法修正
            </div>
            <p className='chat-banner-desc'>支持对文本进行缩写、扩写、 润色等处理，自动优化表达</p>
          </div>
          <div className='chat-banner margin-16 '>
            <SummaryIcon />
            <div className='chat-banner-title'>网页总结</div>
            <p className='chat-banner-desc'>提炼网页核心内容，呈现关 键信息与逻辑脉络，助力用 户快速掌握页面核心价值</p>
          </div>
          <div className='chat-banner'>
            <TranslateIcon />
            <div className='chat-banner-title'>智能翻译</div>
            <p className='chat-banner-desc'>支持多语言实时转换，准确 传递原文语义，消除跨语言 浏览的信息理解障碍</p>
          </div>
        </div>
      </div>
    )
  }
)

export default GuidePage
