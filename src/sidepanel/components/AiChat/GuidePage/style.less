@import '../../../../common/css/var.less';

.guide-page {
  display: flex;
  flex-direction: column;
  color: @body-color;
  font-family: @font-family-base;
  line-height: @line-height-base;
  -webkit-tap-highlight-color: transparent;
  background: @guide-page-bg;
  position: relative;
  border-radius: inherit;
  flex: 1;
  .top-content {
    // width: 100%;
    margin: 76px 32px 22px 32px;
    padding: 24px 32px 24px 32px;
    // height: 222px;
    background: linear-gradient(321deg, #deedff 0%, #def2ff 27%, #f5f8ff 100%);
    border-radius: 24px 24px 24px 24px;
    opacity: 1;
    border: 1px solid #d1ebff;
    position: relative;
    .title {
      font-size: 36px;
      line-height: 36px;
      font-weight: 600;
      color: #1d222c;
    }
    .chat-logo {
      position: absolute;
      width: 147px;
      height: 157px;
      top: -36px;
      right: 68px;
    }
  }
  .chat-banners {
    display: flex;
    flex-direction: row;
    align-items: space-between;
    flex-wrap: nowrap;
    margin: 0 32px;
    .margin-16 {
      margin: 0 16px;
    }
    .chat-banner {
      min-height: 164px;
      flex: 1;
      padding: 16px 24px;
      background: linear-gradient(
        321deg,
        #ebf2ff 4%,
        #e0f6ff 31%,
        #eaf4ff 100%
      );
      border-radius: 8px 8px 8px 8px;
      opacity: 1;
      border: 1px solid #d1ebff;
      .chat-banner-title {
        font-size: 16px;
        font-weight: 600;
      }
      .chat-banner-desc {
        font-size: 11px;
        font-weight: 400;
        color: #6c6f76;
      }
    }
  }
  .chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .center-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .chat-logo {
        width: 98px;
        height: 98px;
      }
      .first-title {
        margin-top: 25px;
        margin-bottom: 12px;
        font-size: 20px;
        font-weight: bold;
        color: @guide-page-title-color;
        line-height: 27px;
      }

      .sub-title {
        font-size: 14px;
        color: @guide-page-subtitle-color;
        line-height: 19px;
      }
    }
  }
}
