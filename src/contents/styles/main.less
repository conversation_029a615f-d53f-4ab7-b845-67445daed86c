/* Web Assistant 主样式文件 */

/* 重置样式 */
* {
  box-sizing: border-box;
}

/* 主容器样式 */
.web-assistant-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  z-index: 2147483647;
  pointer-events: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
}

/* 划词工具栏容器 */
.web-assistant-selection-bar-container {
  position: absolute;
  display: none;
  pointer-events: auto;
  
  &.show {
    display: block;
  }
}

/* 浮动按钮容器 */
.web-assistant-floating-button-container {
  position: fixed;
  pointer-events: auto;
}

/* 确保所有子元素都有正确的指针事件 */
#web-assistant-selection-bar,
#web-assistant-floating-button {
  pointer-events: auto;
}
