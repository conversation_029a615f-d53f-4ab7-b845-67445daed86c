.actionButtons {
  display: flex;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
  flex-wrap: wrap;
  gap: 16px;

  &.hasBothGroups {
    justify-content: space-between;
  }

  &.rightOnly {
    justify-content: flex-end;
  }
}

.leftGroup {
  display: flex;
  align-items: center;
}

.rightGroup {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.actionBtn {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  color: #666;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background: #f0f8ff;
  }

  &:active {
    transform: translateY(1px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      border-color: #d9d9d9;
      color: #666;
      background: #fff;
    }
  }
}

.primaryBtn {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
  font-weight: 500;

  &:hover {
    background: #40a9ff;
    border-color: #40a9ff;
    color: #fff;
  }

  &:disabled {
    background: #d9d9d9;
    border-color: #d9d9d9;
    color: #fff;

    &:hover {
      background: #d9d9d9;
      border-color: #d9d9d9;
      color: #fff;
    }
  }
}



/* 响应式布局 */
@media (max-width: 480px) {
  .actionButtons {
    flex-direction: column;
    gap: 6px;
  }
  
  .actionBtn {
    width: 100%;
    justify-content: center;
  }
}
