# 翻译此页面功能时序图

本文档描述了用户点击"翻译此页面"按钮时的完整执行流程。

## 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant SidePanel as 侧边栏面板
    participant UseTranslate as useTranslate Hook
    participant ChromeAPI as Chrome API
    participant ContentScript as Content Script
    participant BackgroundScript as Background Script
    participant AIService as AI翻译服务
    participant IntersectionObserver as Intersection Observer
    participant MutationObserver as Mutation Observer
    participant BatchProcessor as 批量处理器

    Note over User, BatchProcessor: 用户点击"翻译此页面"按钮
    
    User->>SidePanel: 点击"翻译此页面"按钮
    Note right of SidePanel: 在AiChat组件的composerConfig中<br/>translatePage技能被触发
    
    SidePanel->>UseTranslate: 调用handleTranslate('翻译此页面')
    Note right of UseTranslate: handleTranslateUI函数被调用
    
    UseTranslate->>UseTranslate: reportLog(pageTitle) - 记录用户行为日志
    
    UseTranslate->>ChromeAPI: getCurrentTab() - 获取当前活动标签页
    ChromeAPI-->>UseTranslate: 返回当前标签页信息
    
    UseTranslate->>ChromeAPI: checkContentScriptInjected(tabId) - 检查Content Script是否已注入
    ChromeAPI->>ContentScript: 发送CHECK_CONTENT_SCRIPT消息
    ContentScript-->>ChromeAPI: 返回注入状态
    ChromeAPI-->>UseTranslate: 返回注入状态
    
    alt Content Script未注入
        UseTranslate->>ChromeAPI: refreshCurrentPage(tabId) - 刷新页面
        UseTranslate->>ChromeAPI: 监听页面加载完成事件
        ChromeAPI-->>UseTranslate: 页面加载完成通知
        UseTranslate->>ChromeAPI: 再次检查Content Script注入状态
    end
    
    UseTranslate->>ChromeAPI: 发送START_TRANSLATE消息到Content Script
    Note right of UseTranslate: chrome.tabs.sendMessage(tabId, {type: START_TRANSLATE})
    
    ChromeAPI->>ContentScript: 转发START_TRANSLATE消息
    
    Note over ContentScript: Content Script开始处理翻译请求
    
    ContentScript->>BackgroundScript: 发送CREATE_CONVERSATION消息
    Note right of ContentScript: chrome.runtime.sendMessage({type: CREATE_CONVERSATION})
    
    BackgroundScript->>UseTranslate: 转发CREATE_CONVERSATION消息到消息处理器
    
    UseTranslate->>AIService: 调用createConversation()
    Note right of UseTranslate: POST /ai/orchestration/session/createSession
    
    AIService-->>UseTranslate: 返回会话ID (conversationID)
    UseTranslate-->>BackgroundScript: 返回会话创建结果
    BackgroundScript-->>ContentScript: 返回会话ID
    
    Note over ContentScript: 会话创建成功，开始页面翻译处理
    
    ContentScript->>ContentScript: 设置globalConversationID
    ContentScript->>IntersectionObserver: observerChildElement(document.body) - 观察页面所有元素
    ContentScript->>MutationObserver: handleMutationObserver() - 监听DOM变化
    
    Note over IntersectionObserver: Intersection Observer开始工作
    
    loop 处理可视区域内的元素
        IntersectionObserver->>ContentScript: 元素进入可视区域触发回调
        ContentScript->>ContentScript: processTextNodesInElement() - 处理元素中的文本节点
        
        ContentScript->>ContentScript: 过滤黑名单元素和已翻译元素
        ContentScript->>ContentScript: 检查元素宽度和可见性
        ContentScript->>ContentScript: findNearestBlockElement() - 找到最近的块级元素
        ContentScript->>ContentScript: addTranslateIcon() - 添加翻译图标
        
        ContentScript->>ContentScript: extractStructuredContent() - 提取结构化内容
        ContentScript->>ContentScript: handleTranslateTextContent() - 处理翻译内容
        ContentScript->>BatchProcessor: addToTranslationQueue() - 添加到翻译队列
        
        Note right of BatchProcessor: 设置500ms延迟的批量处理定时器
    end
    
    Note over BatchProcessor: 批量处理定时器触发
    
    BatchProcessor->>BatchProcessor: processBatchTranslation() - 处理批量翻译
    BatchProcessor->>BatchProcessor: groupTextsByCharLimit() - 按字符数限制分组
    
    loop 处理每个翻译组
        alt 批量翻译 (组内多个文本)
            BatchProcessor->>BackgroundScript: 发送BATCH_TRANSLATE消息
            Note right of BatchProcessor: chrome.runtime.sendMessage({<br/>type: BATCH_TRANSLATE,<br/>data: {query: batchQuery, conversationID}})
            
            BackgroundScript->>UseTranslate: 转发BATCH_TRANSLATE消息
            UseTranslate->>AIService: handleTranslateAPI() - 调用翻译API
            Note right of UseTranslate: POST /ai/orchestration/api/v1/chat_query<br/>query: "text1###text2###text3"
            
            AIService-->>UseTranslate: 返回批量翻译结果
            UseTranslate->>UseTranslate: 解析翻译结果 (按###分割)
            UseTranslate-->>BackgroundScript: 返回翻译结果
            BackgroundScript-->>BatchProcessor: 返回翻译结果
            
            BatchProcessor->>ContentScript: updateIconWithTranslation() - 更新每个文本的翻译结果
            
        else 单个翻译 (组内只有一个文本)
            BatchProcessor->>BackgroundScript: 发送SINGLE_TRANSLATE消息
            Note right of BatchProcessor: chrome.runtime.sendMessage({<br/>type: SINGLE_TRANSLATE,<br/>data: {query, conversationID}})
            
            BackgroundScript->>UseTranslate: 转发SINGLE_TRANSLATE消息
            UseTranslate->>AIService: handleTranslateAPI() - 调用翻译API
            Note right of UseTranslate: POST /ai/orchestration/api/v1/chat_query
            
            AIService-->>UseTranslate: 返回翻译结果
            UseTranslate-->>BackgroundScript: 返回翻译结果
            BackgroundScript-->>BatchProcessor: 返回翻译结果
            
            BatchProcessor->>ContentScript: updateIconWithTranslation() - 更新翻译结果
        end
        
        ContentScript->>ContentScript: cleanTranslationText() - 清理翻译文本
        ContentScript->>ContentScript: 比较原文和翻译结果
        
        alt 翻译结果与原文不同
            ContentScript->>ContentScript: 创建翻译结果DOM元素
            ContentScript->>ContentScript: reconstructHtmlStructure() - 重建HTML结构
            ContentScript->>ContentScript: 将翻译结果插入到页面中
            ContentScript->>ContentScript: 隐藏翻译图标
        else 翻译结果与原文相同
            ContentScript->>ContentScript: 直接隐藏翻译图标
        end
    end
    
    BatchProcessor->>BatchProcessor: 清空翻译队列
    
    Note over MutationObserver: 持续监听页面DOM变化
    
    loop 监听新增内容
        MutationObserver->>ContentScript: DOM变化触发回调
        ContentScript->>ContentScript: 过滤翻译相关元素
        ContentScript->>IntersectionObserver: 对新元素进行观察
        Note right of ContentScript: 新增的内容会自动进入翻译流程
    end
    
    Note over User, BatchProcessor: 翻译完成，用户可以看到页面翻译结果
```

## 关键组件说明

### 1. 侧边栏面板 (SidePanel)
- **文件位置**: `src/sidepanel/components/AiChat/index.tsx`
- **作用**: 提供"翻译此页面"按钮界面，配置在`composerConfig.skill`中
- **关键代码**: `onClick: () => { handleTranslate('翻译此页面') }`

### 2. useTranslate Hook
- **文件位置**: `src/common/hooks/useTranslate.tsx`
- **作用**: 统一的翻译处理逻辑，支持UI交互模式和API模式
- **关键方法**:
  - `handleTranslateUI()`: UI交互模式的翻译处理
  - `handleTranslateAPI()`: API翻译请求处理
  - `createConversation()`: 创建AI会话

### 3. Content Script
- **文件位置**: `src/contents/scripts/injectTranslate.ts`
- **作用**: 页面内容处理和翻译逻辑的核心
- **关键功能**:
  - 监听`START_TRANSLATE`消息
  - 管理全局会话ID
  - 处理页面元素的文本提取和翻译

### 4. Intersection Observer
- **作用**: 监听元素进入可视区域，实现懒加载式翻译
- **配置**: `rootMargin: '100px'`, `threshold: 0.2`
- **优化**: 只处理可视区域内的元素，提高性能

### 5. 批量处理器 (Batch Processor)
- **作用**: 优化翻译请求，减少API调用次数
- **策略**: 
  - 500ms延迟批量处理
  - 按1000字符限制分组
  - 使用`###`分隔符连接多个文本

### 6. Mutation Observer
- **作用**: 监听DOM变化，处理动态加载的内容
- **配置**: `childList: true`, `subtree: true`
- **功能**: 自动处理新增的页面内容

## 消息流转

### 消息类型 (MessageType)
- `START_TRANSLATE`: 开始翻译
- `CREATE_CONVERSATION`: 创建会话
- `BATCH_TRANSLATE`: 批量翻译
- `SINGLE_TRANSLATE`: 单个翻译
- `CHECK_CONTENT_SCRIPT`: 检查Content Script状态

### 数据流向
1. **用户操作** → 侧边栏面板
2. **面板** → useTranslate Hook
3. **Hook** → Chrome API → Content Script
4. **Content Script** → Background Script → AI服务
5. **AI服务** → Background Script → Content Script
6. **Content Script** → 页面DOM更新

## 性能优化策略

### 1. 懒加载翻译
- 使用Intersection Observer只翻译可视区域内的内容
- 提前100px开始检测，提升用户体验

### 2. 批量处理
- 500ms延迟合并翻译请求
- 按字符数限制智能分组
- 减少API调用次数

### 3. 内容过滤
- 黑名单过滤不需要翻译的元素
- 跳过已翻译的内容
- 检查元素可见性和尺寸

### 4. 结构保持
- 提取和重建HTML结构信息
- 保持原有的格式和样式
- 支持内联元素的翻译

### 5. 动态监听
- Mutation Observer监听DOM变化
- 自动处理动态加载的内容
- 避免重复处理已翻译元素
