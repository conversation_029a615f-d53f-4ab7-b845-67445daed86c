const ScreenshotIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="16"
      height="16"
      viewBox="0 0 16 16"
    >
      <defs>
        <clipPath id="master_svg0_590_23404">
          <rect x="0" y="0" width="16" height="16" rx="0" />
        </clipPath>
      </defs>
      <g clipPath="url(#master_svg0_590_23404)">
        <g>
          <path
            d="M9.88150453125,3.66333708984375C9.87865453125,3.60192708984375,9.91403453125,3.54513708984375,9.97040453125,3.52061708984375C10.87915453125,3.24412708984375,11.59034453125,2.5329370898437498,11.86684453125,1.62419108984375C11.88524453125,1.56097678984375,11.94324453125,1.51751708984375,12.00904453125,1.51751708984375C12.07494453125,1.51751708984375,12.13284453125,1.56097678984375,12.15124453125,1.62419108984375C12.42774453125,2.53267708984375,13.13874453125,3.24366708984375,14.04724453125,3.52011708984375C14.11044453125,3.53855708984375,14.15384453125,3.59649708984375,14.15384453125,3.66234708984375C14.15384453125,3.72819708984375,14.11044453125,3.78614708984375,14.04724453125,3.80457708984375C13.13864453125,4.081127089843751,12.42764453125,4.79232708984375,12.15124453125,5.70100708984375C12.13304453125,5.76441708984375,12.07504453125,5.80810708984375,12.00904453125,5.80816708984375C11.94324453125,5.80816708984375,11.88524453125,5.76471708984375,11.86684453125,5.70149708984375C11.59034453125,4.7927470898437505,10.87915453125,4.08155708984375,9.97040453125,3.80507708984375C9.91437453125,3.78068708984375,9.87907453125,3.72439708984375,9.88150453125,3.66333708984375ZM4.0012645312499995,2.97142708984375C3.89410453125,2.85224708984375,3.74138453125,2.78417708984375,3.58111453125,2.78417708984375C3.2690745312500002,2.78417708984375,3.01611453125,3.0371370898437497,3.01611453125,3.3491770898437503C3.01611453125,3.4886670898437497,3.06770453125,3.62321708984375,3.16126453125,3.72726708984375L7.13382453125,8.14562708984375L4.99858453125,10.29451708984375C4.7112145312500004,10.15701708984375,4.3893745312500005,10.08001708984375,4.04954453125,10.08001708984375C2.83374653125,10.08001708984375,1.84814453125,11.06561708984375,1.84814453125,12.28141708984375C1.84814453125,13.49721708984375,2.83374653125,14.48281708984375,4.04954453125,14.48281708984375C5.26535453125,14.48281708984375,6.25095453125,13.49721708984375,6.25095453125,12.28141708984375C6.25095453125,11.81651708984375,6.10683453125,11.38526708984375,5.86084453125,11.02991708984375L7.89053453125,8.98725708984375L9.71468453125,11.01610708984375C9.46282453125,11.37406708984375,9.31494453125,11.81051708984375,9.31494453125,12.28141708984375C9.31494453125,13.49721708984375,10.30054453125,14.48281708984375,11.51634453125,14.48281708984375C12.73214453125,14.48281708984375,13.71774453125,13.49721708984375,13.71774453125,12.28141708984375C13.71774453125,11.06561708984375,12.73214453125,10.08001708984375,11.51634453125,10.08001708984375C11.18153453125,10.08001708984375,10.86417453125,10.15476708984375,10.58004453125,10.28847708984375L8.68827453125,8.18440708984375L10.84590453125,6.01298708984375C10.95108453125,5.90712708984375,11.01011453125,5.76396708984375,11.01011453125,5.61474708984375C11.01011453125,5.30270708984375,10.75715453125,5.04974708984375,10.44511453125,5.04974708984375C10.29464453125,5.04974708984375,10.15038453125,5.10976708984375,10.04432453125,5.21650708984375L7.93156453125,7.34277708984375L4.0012645312499995,2.97142708984375ZM4.04954453125,11.21001708984375C4.64126453125,11.21001708984375,5.12095453125,11.68971708984375,5.12095453125,12.28141708984375C5.12095453125,12.87311708984375,4.64126453125,13.35281708984375,4.04954453125,13.35281708984375C3.45782453125,13.35281708984375,2.97814453125,12.87311708984375,2.97814453125,12.28141708984375C2.97814453125,11.68971708984375,3.45782453125,11.21001708984375,4.04954453125,11.21001708984375ZM11.51634453125,11.21001708984375C12.10804453125,11.21001708984375,12.58774453125,11.68971708984375,12.58774453125,12.28141708984375C12.58774453125,12.87311708984375,12.10804453125,13.35281708984375,11.51634453125,13.35281708984375C10.92462453125,13.35281708984375,10.44494453125,12.87311708984375,10.44494453125,12.28141708984375C10.44494453125,11.68971708984375,10.92462453125,11.21001708984375,11.51634453125,11.21001708984375Z"
            fillRule="evenodd"
            fill="currentColor"
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  )
}

const SummaryIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="16"
      height="16"
      viewBox="0 0 16 16"
    >
      <defs>
        <clipPath id="master_svg0_590_23399">
          <rect x="0" y="0" width="16" height="16" rx="0" />
        </clipPath>
      </defs>
      <g clipPath="url(#master_svg0_590_23399)">
        <g>
          <path
            d="M10.416190625,3.91806521484375C10.413150625,3.85254521484375,10.450880625,3.79196521484375,10.511020625,3.76579521484375C11.480490625,3.47083521484375,12.239190625,2.7121252148437502,12.534190625,1.74264721484375C12.553890625,1.67520901484375,12.615690625,1.62884521484375,12.685890625,1.62884521484375C12.756190625,1.62884521484375,12.817990625,1.67520901484375,12.837690625,1.74264721484375C13.132590625,2.71184521484375,13.891090625,3.47034521484375,14.860290625,3.76527521484375C14.927690625,3.78493521484375,14.974090625,3.84675521484375,14.974090625,3.91700521484375C14.974090625,3.98725521484375,14.927690625,4.04907521484375,14.860290625,4.06874521484375C13.890990625,4.36377521484375,13.132390625,5.122495214843751,12.837690625,6.09189521484375C12.818190625,6.15953521484375,12.756290625,6.20615521484375,12.685890625,6.20622521484375C12.615690625,6.20622521484375,12.553890625,6.15985521484375,12.534190625,6.09241521484375C12.239190625,5.1229452148437495,11.480490625,4.364235214843751,10.511020625,4.06926521484375C10.451250625,4.0432452148437505,10.413590625,3.98319521484375,10.416190625,3.91806521484375ZM4.532960624999999,13.19664521484375Q3.036930625,13.19664521484375,1.822596625,13.74414521484375C1.749606625,13.77704521484375,1.670455625,13.79404521484375,1.590390625,13.79404521484375C1.278349625,13.79404521484375,1.025390625,13.54104521484375,1.025390625,13.22904521484375L1.025390625,4.15854521484375C1.025390625,3.95339521484375,1.1365916249999999,3.76436521484375,1.315905625,3.66470521484375Q2.645460625,2.92572521484375,4.532960624999999,2.92572521484375Q6.229550625,2.92572521484375,7.474940625,3.52277521484375Q8.184040625,3.18289521484375,9.039630625,3.03636521484375C9.071130625,3.03097521484375,9.103040625,3.02825521484375,9.135000625,3.02825521484375C9.447040625,3.02825521484375,9.700000625,3.28121521484375,9.700000625,3.59325521484375C9.700000625,3.86849521484375,9.501660625,4.10368521484375,9.230370625,4.15015521484375Q8.577290625,4.26208521484375,8.040160625,4.50596521484375L8.040160625,12.41154521484375Q9.148140625,12.06644521484375,10.417730625,12.06644521484375Q11.687190625,12.06644521484375,12.794790625,12.41144521484375L12.794790625,7.50190521484375C12.794790625,7.18985521484375,13.047790625,6.93690521484375,13.359790625,6.93690521484375C13.671890625,6.93690521484375,13.924790625,7.18985521484375,13.924790625,7.50190521484375L13.924790625,13.22884521484375C13.924790625,13.54084521484375,13.671890625,13.79384521484375,13.359790625,13.79384521484375C13.279690625,13.79384521484375,13.200490625,13.77684521484375,13.127490625,13.74394521484375Q11.913690625,13.19644521484375,10.417730625,13.19644521484375Q8.928300625,13.19644521484375,7.718090625,13.73914521484375C7.573330625,13.80804521484375,7.400350625,13.81514521484375,7.242750625,13.74404521484375Q6.028960625,13.19664521484375,4.532960624999999,13.19664521484375ZM4.532960624999999,12.06664521484375Q5.802410625,12.06664521484375,6.910030625,12.41164521484375L6.910030625,4.50610521484375Q5.918350625,4.055725214843751,4.532960624999999,4.055725214843751Q3.147500625,4.055725214843751,2.155390625,4.50616521484375L2.155390625,12.41164521484375Q3.263380625,12.06664521484375,4.532960624999999,12.06664521484375Z"
            fillRule="evenodd"
            fill="currentColor"
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  )
}

const TranslateIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="16"
      height="16"
      viewBox="0 0 16 16"
    >
      <defs>
        <clipPath id="master_svg0_590_23411">
          <rect x="0" y="0" width="16" height="16" rx="0" />
        </clipPath>
      </defs>
      <g clipPath="url(#master_svg0_590_23411)">
        <g>
          <path
            d="M5.947947109375,13.0772L4.612717109375,13.0772L4.509577109375,13.074C3.776397109375,13.0188,3.203127109375,12.3999,3.203127109375,11.66516L3.203127109375,10.99915C3.203127109375,10.67613,2.940077109375,10.41309,2.617067109375,10.41309C2.2940541093749998,10.41309,2.0310061093750003,10.67613,2.0310061093750003,10.99915L2.0310061093750003,11.66516C2.0310061093750003,13.0907,3.190337109375,14.2501,4.615917109375,14.2501L5.948747109375,14.2501C6.271757109375,14.2501,6.534007109375,13.987,6.534807109375,13.664C6.534807109375,13.341,6.271757109375,13.0779,5.948747109375,13.0772L5.947947109375,13.0772ZM14.617837109375,13.9017L11.946637109375,7.224C11.905037109375,7.13605,11.817037109375,7.08008,11.717137109375,7.08008L10.836047109375,7.08008C10.732107109375,7.08008,10.640157109375,7.14244,10.600977109375,7.23919L7.936117109375,13.9017C7.924117109375,13.9321,7.917727109375,13.9633,7.917727109375,13.9961C7.917727109375,14.136,8.031257109375,14.2495,8.171177109375,14.2495L8.888367109375,14.2495C8.992307109375,14.2495,9.085047109375001,14.1872,9.123427109375001,14.0904L9.858207109375,12.2507L12.691737109375,12.2507L13.428137109375,14.0904C13.466537109375,14.1872,13.559237109375,14.2495,13.663237109375,14.2495L14.382037109375,14.2495C14.414737109375,14.2495,14.445937109375,14.2431,14.476337109375,14.2312C14.605837109375,14.1792,14.669037109375,14.0313,14.617037109375,13.9017L14.617837109375,13.9017ZM10.327537109375,11.07777L11.276587109375,8.707139999999999L12.224037109375,11.07777L10.327537109375,11.07777ZM7.613037109375,3.08283L5.200827109375,3.08283L5.200827109375,2.003454C5.200827109375,1.863535,5.087287109375,1.75,4.947367109375,1.75L4.281357109375,1.75C4.1414371093749995,1.75,4.027897109375,1.863535,4.027897109375,2.003454L4.027897109375,3.08283L1.616491109375,3.08283C1.476572109375,3.08283,1.363037109375,3.19637,1.363037109375,3.33629L1.363037109375,7.33398C1.363037109375,7.4739,1.476572109375,7.58744,1.616491109375,7.58744L4.028697109375,7.58744L4.028697109375,9.332830000000001C4.028697109375,9.47275,4.1422371093749994,9.58628,4.2821571093749995,9.58628L4.9481671093749995,9.58628C5.0880871093749995,9.58628,5.201627109375,9.47275,5.201627109375,9.332830000000001L5.201627109375,7.58744L7.613837109375,7.58744C7.753757109375,7.58744,7.867287109375,7.4739,7.867287109375,7.33398L7.867287109375,3.33549C7.867287109375,3.19557,7.753757109375,3.08203,7.613837109375,3.08203L7.613037109375,3.08283ZM4.027897109375,6.41371L2.535957109375,6.41371L2.535957109375,4.2549600000000005L4.028697109375,4.2549600000000005L4.028697109375,6.41371L4.027897109375,6.41371ZM6.692767109375,6.41371L5.200027109375,6.41371L5.200027109375,4.2549600000000005L6.692767109375,4.2549600000000005L6.692767109375,6.41371ZM9.945197109375,3.5891200000000003L11.278027109375,3.5891200000000003C12.056737109375,3.5891200000000003,12.690837109375,4.22315,12.690837109375,5.0019L12.690837109375,5.6679200000000005C12.690837109375,5.99093,12.953837109375,6.25318,13.276837109375,6.25398C13.599837109375,6.25398,13.862937109375,5.99093,13.862937109375,5.6679200000000005L13.862937109375,5.0019C13.862937109375,3.57632,12.703637109375,2.416992,11.278027109375,2.416992L9.945197109375,2.416992C9.622177109375,2.416992,9.359127109374999,2.680041,9.359127109374999,3.00305C9.359127109374999,3.32607,9.622177109375,3.5891200000000003,9.945197109375,3.5891200000000003Z"
            fill="currentColor"
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  )
}

const CustomIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="19"
      height="17"
      viewBox="0 0 19 17"
    >
      <g>
        <g>
          <rect
            x="1"
            y="12.307921409606934"
            width="3.6429548263549805"
            height="1.3846526145935059"
            rx="0"
            fill="#000000"
            fillOpacity="1"
            style={{ mixBlendMode: 'passthrough' }}
          />
          <rect
            x="1"
            y="12.307921409606934"
            width="3.6429548263549805"
            height="1.3846526145935059"
            rx="0"
            fillOpacity="0"
            strokeOpacity="1"
            stroke="#000000"
            fill="none"
            strokeWidth="0.4"
          />
        </g>
        <g>
          <path
            d="M14.356445443725587,4.462007522583008L12.094895443725587,4.462007522583008L7.451345443725586,16.00080752258301L9.577855443725586,16.00080752258301L10.681435443725587,13.107777522583008L15.767965443725586,13.107777522583008L16.874455443725587,16.00080752258301L19.000045443725586,16.00080752258301L14.356445443725587,4.462007522583008ZM11.276935443725586,11.556047522583007L13.200415443725586,6.498367522583008L13.266475443725586,6.498367522583008L15.172465443725585,11.556047522583007L11.276935443725586,11.556047522583007Z"
            fill="#1677FF"
            fillOpacity="1"
            style={{ mixBlendMode: 'passthrough' }}
          />
        </g>
        <g>
          <rect
            x="1"
            y="6.653472423553467"
            width="6.071592330932617"
            height="1.3846526145935059"
            rx="0"
            fill="#000000"
            fillOpacity="1"
            style={{ mixBlendMode: 'passthrough' }}
          />
          <rect
            x="1"
            y="6.653472423553467"
            width="6.071592330932617"
            height="1.3846526145935059"
            rx="0"
            fillOpacity="0"
            strokeOpacity="1"
            stroke="#000000"
            fill="none"
            strokeWidth="0.4"
          />
        </g>
        <g>
          <rect
            x="1"
            y="1"
            width="17.000457763671875"
            height="1.3846526145935059"
            rx="0"
            fill="#000000"
            fillOpacity="1"
            style={{ mixBlendMode: 'passthrough' }}
          />
          <rect
            x="1"
            y="1"
            width="17.000457763671875"
            height="1.3846526145935059"
            rx="0"
            fillOpacity="0"
            strokeOpacity="1"
            stroke="#000000"
            fill="none"
            strokeWidth="0.4"
          />
        </g>
      </g>
    </svg>
  )
}


export { ScreenshotIcon, SummaryIcon, TranslateIcon, CustomIcon }

export default () => null
