/**
 * AI 服务配置
 */

import { AIService } from '../services/aiService';
import { MockAIService } from '../services/mockAIService';

/**
 * AI 配置接口
 */
export interface AIConfig {
  useMock: boolean;
  apiUrl: string;
  apiKey?: string;
  timeout: number;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: AIConfig = {
  useMock: true, // 开发环境默认使用 mock
  apiUrl: 'http://localhost:3000/api',
  timeout: 30000, // 30秒超时
};

/**
 * 获取当前配置
 */
export function getAIConfig(): AIConfig {
  // 可以从 chrome.storage 或环境变量中读取配置
  return {
    ...DEFAULT_CONFIG,
    // 生产环境可以设置为 false
    useMock: process.env.NODE_ENV !== 'production',
  };
}

/**
 * 获取 AI 服务实例
 */
export function getAIService(): AIService {
  const config = getAIConfig();
  
  if (config.useMock) {
    console.log('Using Mock AI Service for development');
    return new MockAIService(config.apiUrl, config.apiKey);
  } else {
    console.log('Using Real AI Service');
    return new AIService(config.apiUrl, config.apiKey);
  }
}

/**
 * 更新配置
 */
export async function updateAIConfig(newConfig: Partial<AIConfig>): Promise<void> {
  // 这里可以实现配置的持久化存储
  // 例如使用 chrome.storage.local
  console.log('Updating AI config:', newConfig);
  // TODO: 实现配置存储逻辑
}
