# Chrome浏览器插件中Shadow DOM实现指南

## 目录
1. [技术背景](#技术背景)
2. [Shadow DOM实现要点](#shadow-dom实现要点)
3. [样式隔离实现](#样式隔离实现)
4. [事件处理机制](#事件处理机制)
5. [常见问题和解决方案](#常见问题和解决方案)
6. [代码示例](#代码示例)
7. [调试和测试建议](#调试和测试建议)
8. [性能优化注意事项](#性能优化注意事项)

## 技术背景

### 为什么在浏览器插件中使用Shadow DOM？

在Chrome浏览器插件开发中，Content Script需要在宿主页面中注入UI组件。传统的DOM注入方式存在以下问题：

1. **样式冲突**：插件的CSS样式可能被宿主页面的样式覆盖或影响
2. **全局污染**：插件的DOM元素可能影响宿主页面的布局和功能
3. **事件冲泡**：插件的事件可能被宿主页面的事件处理器捕获
4. **选择器冲突**：宿主页面的JavaScript可能意外选择到插件的DOM元素

Shadow DOM提供了完美的解决方案：
- **样式封装**：Shadow DOM内的样式完全隔离，不会与宿主页面冲突
- **DOM封装**：Shadow DOM内的元素对宿主页面不可见
- **事件边界**：事件在Shadow DOM边界处有特殊的处理机制

## Shadow DOM实现要点

### 1. 创建和挂载Shadow Root

```typescript
// 创建主容器
const mainContainer = document.createElement('div');
mainContainer.id = 'web-assistant-main-container';
mainContainer.className = 'web-assistant-container';

// 创建Shadow Root - 关键配置
const shadowRoot = mainContainer.attachShadow({ mode: 'open' });

// 添加到页面
document.body.appendChild(mainContainer);
```

**关键配置说明：**
- `mode: 'open'`：允许外部JavaScript访问Shadow DOM（用于调试）
- `mode: 'closed'`：完全封闭，外部无法访问（更安全但难以调试）

### 2. 样式注入策略

```typescript
// 导入样式文本（使用Plasmo的data-text导入）
import selectionBarStyleText from "data-text:../components/SelectionBar/index.module.less";
import floatingButtonStyleText from "data-text:../components/FloatingButton/index.module.less";
import aiProcessModalStyleText from "data-text:../components/AIProcessModal/index.module.less";

// 创建样式元素并注入
const style = document.createElement('style');
style.textContent = this.getStyles() + '\n' + 
                   selectionBarStyleText + '\n' + 
                   floatingButtonStyleText + '\n' + 
                   aiProcessModalStyleText;
shadowRoot.appendChild(style);
```

### 3. 容器结构设计

```typescript
// 主容器样式 - 关键配置
const mainContainerStyles = `
  .web-assistant-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 0;          /* 不占用空间 */
    height: 0;         /* 不占用空间 */
    z-index: 2147483647; /* 最高层级 */
    pointer-events: none; /* 默认不拦截事件 */
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  }
`;
```

## 样式隔离实现

### 1. CSS模块化处理

项目使用Less模块化，确保样式的作用域隔离：

```less
// SelectionBar/index.module.less
.selectionBar {
  position: absolute;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  font-family: sans-serif; /* 重置字体，避免继承宿主页面样式 */
}

.selectionDropdown {
  /* 确保在Shadow DOM中可见的关键样式 */
  opacity: 1 !important;
  visibility: visible !important;
  transform: translateY(0) !important;
  display: block !important;
}
```

### 2. 样式重置策略

```css
/* 在Shadow DOM中重置所有样式 */
* {
  box-sizing: border-box;
}

/* 确保子元素有正确的指针事件 */
#web-assistant-selection-bar,
#web-assistant-floating-button {
  pointer-events: auto;
}
```

## 事件处理机制

### 1. 事件冒泡阻止

在Shadow DOM中，需要特别注意事件的传播机制：

```typescript
const toggleDropdown = (e: React.MouseEvent) => {
  e.stopPropagation();
  e.preventDefault();
  e.nativeEvent.stopImmediatePropagation(); // 阻止原生事件的立即传播
  setShowDropdown(!showDropdown);
};

const handleAction = (e: React.MouseEvent) => {
  e.stopPropagation();
  e.preventDefault();
  // 处理具体动作
};
```

### 2. 文档级事件监听

```typescript
private handleDocumentClick = (event: MouseEvent): void => {
  if (this.isSelectionBarVisible && this.selectionBarContainer) {
    const target = event.target as Node;
    
    // 使用composedPath获取完整的事件路径，包括Shadow DOM内的元素
    const eventPath = event.composedPath();
    
    // 检查事件路径中是否包含我们的容器
    const isInsideMainContainer = this.mainContainer && eventPath.includes(this.mainContainer);
    const isInsideShadowDOM = this.shadowRoot && eventPath.some(node =>
      this.shadowRoot!.contains(node as Node)
    );
    
    // 如果点击在组件内部，不隐藏工具栏
    if (isInsideShadowDOM || isInsideMainContainer) {
      return;
    }
    
    // 否则隐藏工具栏
    this.hideSelectionBar();
  }
};
```

## 常见问题和解决方案

### 1. 样式穿透问题

**问题**：某些CSS属性可能会从宿主页面穿透到Shadow DOM中。

**解决方案**：
```css
/* 在Shadow DOM的根样式中重置所有可能被继承的属性 */
:host {
  all: initial;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
```

### 2. 事件处理冲突

**问题**：Shadow DOM内的事件可能被宿主页面的事件处理器影响。

**解决方案**：
```typescript
// 在所有交互元素上添加事件阻止
onMouseDown={(e) => {
  e.stopPropagation();
  e.preventDefault();
}}
onMouseUp={(e) => {
  e.stopPropagation();
  e.preventDefault();
}}
```

### 3. 定位计算问题

**问题**：Shadow DOM内元素的定位计算可能不准确。

**解决方案**：
```typescript
// 使用getBoundingClientRect获取准确的位置信息
const selection = window.getSelection();
if (selection && selection.rangeCount > 0) {
  const range = selection.getRangeAt(0);
  const rect = range.getBoundingClientRect();
  
  // rect已经是相对于视口的位置，可以直接使用
  let left = rect.left;
  let top = rect.bottom + offsetY;
  
  // 边界检查
  left = Math.max(5, Math.min(left, window.innerWidth - elementWidth));
  top = Math.max(5, Math.min(top, window.innerHeight - elementHeight));
}
```

### 4. React组件渲染问题

**问题**：React组件在Shadow DOM中可能无法正常渲染。

**解决方案**：
```typescript
// 使用createRoot API正确挂载React组件
import { createRoot } from 'react-dom/client';

const container = document.createElement('div');
shadowRoot.appendChild(container);

const root = createRoot(container);
root.render(React.createElement(YourComponent, props));
```

## 代码示例

### 完整的WebAssistantManager实现

```typescript
class WebAssistantManager {
  private mainContainer: HTMLDivElement | null = null;
  private shadowRoot: ShadowRoot | null = null;
  private selectionBarContainer: HTMLDivElement | null = null;
  private selectionBarRoot: Root | null = null;

  constructor() {
    this.init();
  }

  private init(): void {
    // 防止重复初始化
    const existingContainer = document.getElementById('web-assistant-main-container');
    if (existingContainer) {
      return;
    }

    this.createMainContainer();
    this.initEventListeners();
    this.initFloatingButton();
  }

  private createMainContainer(): void {
    // 创建主容器
    this.mainContainer = document.createElement('div');
    this.mainContainer.id = 'web-assistant-main-container';
    this.mainContainer.className = 'web-assistant-container';

    // 创建Shadow Root
    this.shadowRoot = this.mainContainer.attachShadow({ mode: 'open' });

    // 注入样式
    const style = document.createElement('style');
    style.textContent = this.getStyles() + '\n' +
                       selectionBarStyleText + '\n' +
                       floatingButtonStyleText + '\n' +
                       aiProcessModalStyleText;
    this.shadowRoot.appendChild(style);

    // 创建子容器
    this.selectionBarContainer = document.createElement('div');
    this.selectionBarContainer.id = 'web-assistant-selection-bar';
    this.selectionBarContainer.className = 'web-assistant-selection-bar-container';
    this.shadowRoot.appendChild(this.selectionBarContainer);

    // 添加到页面
    document.body.appendChild(this.mainContainer);
  }

  private getStyles(): string {
    return `
      /* 重置样式 */
      * {
        box-sizing: border-box;
      }

      /* 主容器样式 */
      .web-assistant-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        z-index: 2147483647;
        pointer-events: none;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.4;
      }

      /* 子容器样式 */
      .web-assistant-selection-bar-container {
        position: absolute;
        display: none;
        pointer-events: auto;
      }

      .web-assistant-selection-bar-container.show {
        display: block;
      }
    `;
  }
}
```

### React组件中的事件处理最佳实践

```typescript
const SelectionBar: React.FC<SelectionBarProps> = ({ selectedText, onAction, onClose }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  // 正确的事件处理方式
  const handleAction = (action: string) => {
    // 处理AI操作
    if (isAIAction(action)) {
      setCurrentAction(action);
      setShowModal(true);
      setShowDropdown(false);
    } else {
      onAction(action);
      if (action !== 'open-panel') {
        onClose();
      }
    }
  };

  // 下拉菜单切换 - 关键的事件阻止
  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    e.nativeEvent.stopImmediatePropagation();
    setShowDropdown(!showDropdown);
  };

  // 通用的事件处理属性
  const commonEventProps = {
    onClick: (e: React.MouseEvent) => {
      e.stopPropagation();
      e.preventDefault();
      handleAction(item.action);
    },
    onMouseDown: (e: React.MouseEvent) => {
      e.stopPropagation();
      e.preventDefault();
    }
  };

  return (
    <div className={styles.selectionContainer}>
      <div className={styles.selectionBar}>
        {/* 主要按钮 */}
        {getMainButtons().map(item => (
          <div
            key={item.id}
            className={styles.selectionButton}
            {...commonEventProps}
          >
            <span>{item.label}</span>
          </div>
        ))}

        {/* 更多选项下拉菜单 */}
        <div className={styles.selectionMore}>
          <div
            className={styles.selectionDots}
            onClick={toggleDropdown}
            onMouseDown={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            ⋮
          </div>
          {showDropdown && (
            <div
              className={styles.selectionDropdown}
              onClick={(e) => e.stopPropagation()}
            >
              {getDropdownItems().map((item) => (
                <div
                  key={item.id}
                  className={styles.selectionDropdownItem}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAction(item.action);
                  }}
                >
                  {item.label}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
```

## 调试和测试建议

### 1. 开发者工具调试

**Shadow DOM调试技巧：**

1. **查看Shadow DOM结构**：
   - 在Chrome开发者工具中，Shadow DOM会显示为 `#shadow-root (open)`
   - 可以展开查看内部结构

2. **样式调试**：
   ```javascript
   // 在控制台中访问Shadow DOM
   const container = document.getElementById('web-assistant-main-container');
   const shadowRoot = container.shadowRoot;
   console.log(shadowRoot.innerHTML);
   ```

3. **事件调试**：
   ```javascript
   // 监听Shadow DOM内的事件
   shadowRoot.addEventListener('click', (e) => {
     console.log('Shadow DOM click:', e.target, e.composedPath());
   });
   ```

### 2. 常用调试代码

```typescript
// 调试事件传播
private handleDocumentClick = (event: MouseEvent): void => {
  const eventPath = event.composedPath();
  console.log('Document click debug:', {
    target: event.target,
    eventPathLength: eventPath.length,
    eventPath: eventPath.map(node => ({
      nodeName: (node as Element).nodeName,
      className: (node as Element).className,
      id: (node as Element).id
    }))
  });
};

// 调试样式应用
private debugStyles(): void {
  if (this.shadowRoot) {
    const styleElement = this.shadowRoot.querySelector('style');
    console.log('Applied styles:', styleElement?.textContent);
  }
}

// 调试组件渲染
useEffect(() => {
  console.log('SelectionBar render state:', {
    showDropdown,
    showModal,
    selectedText: selectedText.slice(0, 50)
  });
}, [showDropdown, showModal, selectedText]);
```

### 3. 测试策略

**单元测试：**
```typescript
// 测试Shadow DOM创建
describe('WebAssistantManager', () => {
  test('should create shadow root correctly', () => {
    const manager = new WebAssistantManager();
    const container = document.getElementById('web-assistant-main-container');

    expect(container).toBeTruthy();
    expect(container.shadowRoot).toBeTruthy();
    expect(container.shadowRoot.mode).toBe('open');
  });

  test('should inject styles correctly', () => {
    const manager = new WebAssistantManager();
    const container = document.getElementById('web-assistant-main-container');
    const styleElement = container.shadowRoot.querySelector('style');

    expect(styleElement).toBeTruthy();
    expect(styleElement.textContent).toContain('.web-assistant-container');
  });
});
```

**集成测试：**
```typescript
// 测试事件隔离
test('should not interfere with host page events', () => {
  const hostClickHandler = jest.fn();
  document.addEventListener('click', hostClickHandler);

  const manager = new WebAssistantManager();
  // 模拟Shadow DOM内的点击
  const shadowButton = container.shadowRoot.querySelector('.selection-button');
  shadowButton.click();

  // 宿主页面的事件处理器不应该被触发
  expect(hostClickHandler).not.toHaveBeenCalled();
});
```

## 性能优化注意事项

### 1. 样式优化

**避免重复样式注入：**
```typescript
private static styleInjected = false;

private injectStyles(): void {
  if (WebAssistantManager.styleInjected) {
    return;
  }

  const style = document.createElement('style');
  style.textContent = this.getStyles();
  this.shadowRoot.appendChild(style);

  WebAssistantManager.styleInjected = true;
}
```

**使用CSS变量减少样式体积：**
```css
:host {
  --primary-color: #007bff;
  --border-radius: 8px;
  --shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.selection-bar {
  background: var(--primary-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}
```

### 2. DOM操作优化

**批量DOM操作：**
```typescript
private createContainers(): void {
  // 使用DocumentFragment减少重排
  const fragment = document.createDocumentFragment();

  this.selectionBarContainer = document.createElement('div');
  this.floatingButtonContainer = document.createElement('div');

  fragment.appendChild(this.selectionBarContainer);
  fragment.appendChild(this.floatingButtonContainer);

  this.shadowRoot.appendChild(fragment);
}
```

**事件委托：**
```typescript
// 在Shadow Root上使用事件委托
this.shadowRoot.addEventListener('click', (e) => {
  const target = e.target as Element;

  if (target.matches('.selection-button')) {
    this.handleButtonClick(target);
  } else if (target.matches('.dropdown-item')) {
    this.handleDropdownClick(target);
  }
});
```

### 3. 内存管理

**正确清理资源：**
```typescript
public destroy(): void {
  // 清理事件监听器
  document.removeEventListener('mouseup', this.handleMouseUp);
  document.removeEventListener('click', this.handleDocumentClick);

  // 清理React根节点
  if (this.selectionBarRoot) {
    this.selectionBarRoot.unmount();
    this.selectionBarRoot = null;
  }

  // 移除DOM元素
  if (this.mainContainer && this.mainContainer.parentNode) {
    this.mainContainer.parentNode.removeChild(this.mainContainer);
  }

  // 清理引用
  this.mainContainer = null;
  this.shadowRoot = null;
  this.selectionBarContainer = null;
}
```

### 4. 渲染性能优化

**使用React.memo优化组件渲染：**
```typescript
const SelectionBar = React.memo<SelectionBarProps>(({ selectedText, onAction, onClose }) => {
  // 组件实现
}, (prevProps, nextProps) => {
  // 自定义比较逻辑
  return prevProps.selectedText === nextProps.selectedText &&
         prevProps.onAction === nextProps.onAction &&
         prevProps.onClose === nextProps.onClose;
});
```

**延迟加载非关键组件：**
```typescript
const AIProcessModal = React.lazy(() => import('../AIProcessModal'));

// 在需要时才渲染
{showModal && (
  <React.Suspense fallback={<div>Loading...</div>}>
    <AIProcessModal {...modalProps} />
  </React.Suspense>
)}
```

## 总结

Shadow DOM在Chrome浏览器插件开发中提供了完美的样式和DOM隔离解决方案。通过正确的实现方式，可以确保插件组件与宿主页面完全隔离，避免样式冲突和事件干扰。

**关键要点回顾：**

1. **正确创建Shadow Root**：使用`attachShadow({ mode: 'open' })`
2. **样式完全隔离**：通过Shadow DOM边界实现样式封装
3. **事件处理机制**：使用`composedPath()`和正确的事件阻止
4. **React集成**：使用`createRoot` API正确挂载组件
5. **性能优化**：避免重复操作，正确管理内存
6. **调试策略**：利用开发者工具和日志进行调试

遵循本指南的最佳实践，可以构建出稳定、高性能的Chrome浏览器插件UI组件。
```
